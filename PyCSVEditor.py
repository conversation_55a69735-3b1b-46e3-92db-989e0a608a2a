import sys
import csv
import re
from typing import List, Tuple
from PySide6.QtWidgets import (
	QApplication,
	QMainWindow,
	QVBoxLayout,
	QHBoxLayout,
	QWidget,
	QPushButton,
	QTableWidget,
	QTableWidgetItem,
	QLineEdit,
	QLabel,
	QMessageBox,
	QFileDialog,
	QSplitter,
	QGroupBox,
	QListWidget,
)
from PySide6.QtCore import Qt


class PresetEditor(QMainWindow):
	def __init__(self):
		super().__init__()
		self.setWindowTitle("Preset Editor")
		self.setGeometry(100, 100, 1200, 800)

		self.presets = []
		self.current_preset_index = -1
		self.character_name = ""
		self.csv_file = ""

		self.setup_ui()

	def setup_ui(self):
		central_widget = QWidget()
		self.setCentralWidget(central_widget)

		main_layout = QVBoxLayout(central_widget)

		# File operations
		file_layout = QHBoxLayout()
		self.load_btn = QPushButton("Load CSV")
		self.save_btn = QPushButton("Save CSV")
		self.load_btn.clicked.connect(self.load_csv)
		self.save_btn.clicked.connect(self.save_csv)
		file_layout.addWidget(self.load_btn)
		file_layout.addWidget(self.save_btn)
		file_layout.addStretch()
		main_layout.addLayout(file_layout)

		# Main editing area
		splitter = QSplitter(Qt.Orientation.Horizontal)
		main_layout.addWidget(splitter)

		# Left side - Preset list and controls
		left_group = QGroupBox("Presets")
		left_layout = QVBoxLayout(left_group)

		# Preset list widget
		self.preset_list = QListWidget()
		self.preset_list.currentRowChanged.connect(self.preset_changed)
		left_layout.addWidget(self.preset_list)

		# Preset action buttons
		preset_buttons_layout = QVBoxLayout()
		self.add_preset_btn = QPushButton("Add Preset")
		self.duplicate_preset_btn = QPushButton("Duplicate Preset")
		self.delete_preset_btn = QPushButton("Delete Preset")
		self.add_preset_btn.clicked.connect(self.add_preset)
		self.duplicate_preset_btn.clicked.connect(self.duplicate_preset)
		self.delete_preset_btn.clicked.connect(self.delete_preset)

		preset_buttons_layout.addWidget(self.add_preset_btn)
		preset_buttons_layout.addWidget(self.duplicate_preset_btn)
		preset_buttons_layout.addWidget(self.delete_preset_btn)
		left_layout.addLayout(preset_buttons_layout)

		splitter.addWidget(left_group)

		# Right side - Vertical layout for Basic Info and Meshes & Materials
		right_widget = QWidget()
		right_main_layout = QVBoxLayout(right_widget)

		# Basic Info groupbox (now on top right)
		basic_info_group = QGroupBox("Basic Info")
		basic_info_layout = QVBoxLayout(basic_info_group)

		# Character name (read-only)
		char_name_layout = QHBoxLayout()
		char_name_layout.addWidget(QLabel("Character:"))
		self.character_name_label = QLabel("(No character loaded)")
		self.character_name_label.setStyleSheet("font-weight: bold; color: #0066cc;")
		char_name_layout.addWidget(self.character_name_label)
		char_name_layout.addStretch()
		basic_info_layout.addLayout(char_name_layout)

		# Preset name
		name_layout = QHBoxLayout()
		name_layout.addWidget(QLabel("Name:"))
		self.preset_name_edit = QLineEdit()
		self.preset_name_edit.textChanged.connect(self.update_current_preset)
		name_layout.addWidget(self.preset_name_edit)
		basic_info_layout.addLayout(name_layout)

		# Leader mesh
		leader_layout = QHBoxLayout()
		leader_layout.addWidget(QLabel("Leader Mesh:"))
		self.leader_mesh_edit = QLineEdit()
		self.leader_mesh_edit.textChanged.connect(self.update_current_preset)
		leader_layout.addWidget(self.leader_mesh_edit)
		basic_info_layout.addLayout(leader_layout)

		# Leader material
		leader_mat_layout = QHBoxLayout()
		leader_mat_layout.addWidget(QLabel("Leader Material:"))
		self.leader_material_edit = QLineEdit()
		self.leader_material_edit.textChanged.connect(self.update_current_preset)
		leader_mat_layout.addWidget(self.leader_material_edit)
		basic_info_layout.addLayout(leader_mat_layout)

		right_main_layout.addWidget(basic_info_group)

		# Skeletal Meshes & Materials groupbox (now below Basic Info)
		meshes_group = QGroupBox("Skeletal Meshes & Materials")
		meshes_layout = QVBoxLayout(meshes_group)

		# Add/Remove buttons
		mesh_buttons = QHBoxLayout()
		self.add_mesh_btn = QPushButton("Add Mesh & Material")
		self.remove_mesh_btn = QPushButton("Remove Selected")
		self.add_mesh_btn.clicked.connect(self.add_mesh_material)
		self.remove_mesh_btn.clicked.connect(self.remove_mesh_material)
		mesh_buttons.addWidget(self.add_mesh_btn)
		mesh_buttons.addWidget(self.remove_mesh_btn)
		mesh_buttons.addStretch()
		meshes_layout.addLayout(mesh_buttons)

		# Table for meshes and materials
		self.mesh_table = QTableWidget()
		self.mesh_table.setColumnCount(2)
		self.mesh_table.setHorizontalHeaderLabels(["Skeletal Mesh", "Material"])
		self.mesh_table.horizontalHeader().setStretchLastSection(True)
		self.mesh_table.itemChanged.connect(self.table_item_changed)
		meshes_layout.addWidget(self.mesh_table)

		right_main_layout.addWidget(meshes_group)
		splitter.addWidget(right_widget)
		splitter.setSizes([300, 900])

		# Initially disable editing controls
		self.set_editing_enabled(False)

	def set_editing_enabled(self, enabled):
		self.preset_name_edit.setEnabled(enabled)
		self.leader_mesh_edit.setEnabled(enabled)
		self.leader_material_edit.setEnabled(enabled)
		self.mesh_table.setEnabled(enabled)
		self.add_mesh_btn.setEnabled(enabled)
		self.remove_mesh_btn.setEnabled(enabled)
		self.add_preset_btn.setEnabled(enabled)
		self.duplicate_preset_btn.setEnabled(enabled)
		self.delete_preset_btn.setEnabled(enabled)
		self.save_btn.setEnabled(enabled)

	def extract_character_name(self, path):
		"""Extract character name from a path like /Game/Lia/Meshes/..."""
		match = re.search(r"/Game/([^/]+)/", path)
		return match.group(1) if match else ""

	def simplify_path(self, path, is_material=False):
		"""Simplify path for display by removing /Game/{character}/ prefix and duplicate extension"""
		if not path or not self.character_name:
			return path

		game_prefix = f"/Game/{self.character_name}/"
		if path.startswith(game_prefix):
			simplified = path[len(game_prefix):]

			# Remove duplicate extension (e.g., SK_Hair.SK_Hair -> SK_Hair)
			parts = simplified.split("/")
			if parts:
				filename = parts[-1]
				if "." in filename:
					base_name = filename.split(".")[0]
					# Check if the extension matches the base name (duplicate extension)
					if filename == f"{base_name}.{base_name}":
						parts[-1] = base_name
						simplified = "/".join(parts)

			# Ensure it starts with /
			if not simplified.startswith("/"):
				simplified = "/" + simplified
			return simplified
		return path

	def expand_path(self, simplified_path, is_material=False):
		"""Convert simplified path back to full format with /Game/{character}/ prefix and duplicate extension"""
		if not simplified_path or not self.character_name:
			return simplified_path

		# Ensure the simplified path starts with /
		if not simplified_path.startswith("/"):
			simplified_path = "/" + simplified_path

		# Add the /Game/{character} prefix
		full_path = f"/Game/{self.character_name}{simplified_path}"

		# Add duplicate extension if not already present
		parts = full_path.split("/")
		if parts:
			filename = parts[-1]
			# If filename doesn't contain a dot, or doesn't have duplicate extension, add it
			if "." not in filename:
				parts[-1] = f"{filename}.{filename}"
				full_path = "/".join(parts)
			elif not filename.endswith(f".{filename.split('.')[0]}"):
				# If it has an extension but not duplicate, make it duplicate
				base_name = filename.split(".")[0]
				parts[-1] = f"{base_name}.{base_name}"
				full_path = "/".join(parts)

		return full_path

	def parse_array_string(self, array_str):
		"""Parse the array string format from CSV"""
		if not array_str or array_str == "":
			return []
		# Remove outer parentheses and split by comma
		array_str = array_str.strip("()")
		items = []
		current_item = ""
		in_quotes = False

		for char in array_str:
			if char == '"' and (not current_item or current_item[-1] != "\\"):
				in_quotes = not in_quotes
			elif char == "," and not in_quotes:
				if current_item.strip():
					items.append(current_item.strip().strip('"'))
				current_item = ""
			else:
				current_item += char

		if current_item.strip():
			items.append(current_item.strip().strip('"'))

		return items

	def format_array_string(self, items):
		"""Format items back to CSV array format"""
		if not items:
			return ""
		quoted_items = [f'"{item}"' for item in items]
		return f"({','.join(quoted_items)})"

	def load_csv(self):
		file_path, _ = QFileDialog.getOpenFileName(
			self, "Load CSV File", "", "CSV Files (*.csv)"
		)
		if not file_path:
			return

		try:
			with open(file_path, "r", encoding="utf-8") as file:
				reader = csv.DictReader(file)
				self.presets = list(reader)

			if self.presets:
				# Extract character name from the first preset
				first_preset = self.presets[0]
				sample_path = first_preset.get("SkeletalMeshes", "")
				if sample_path:
					meshes = self.parse_array_string(sample_path)
					if meshes:
						self.character_name = self.extract_character_name(meshes[0])
						self.character_name_label.setText(self.character_name)

				self.csv_file = file_path
				self.update_preset_list()
				self.set_editing_enabled(True)

		except Exception as e:
			QMessageBox.critical(self, "Error", f"Failed to load CSV: {str(e)}")

	def save_csv(self):
		if not self.csv_file:
			file_path, _ = QFileDialog.getSaveFileName(
				self, "Save CSV File", "", "CSV Files (*.csv)"
			)
			if not file_path:
				return
			self.csv_file = file_path

		try:
			with open(self.csv_file, "w", newline="", encoding="utf-8") as file:
				if self.presets:
					fieldnames = [
						"PresetName",
						"LeaderMesh",
						"LeaderMeshMaterial",
						"SkeletalMeshes",
						"Materials",
					]
					writer = csv.DictWriter(file, fieldnames=fieldnames)
					writer.writeheader()
					writer.writerows(self.presets)

			QMessageBox.information(self, "Success", "CSV file saved successfully!")

		except Exception as e:
			QMessageBox.critical(self, "Error", f"Failed to save CSV: {str(e)}")

	def update_preset_list(self):
		self.preset_list.clear()
		for i, preset in enumerate(self.presets):
			self.preset_list.addItem(preset.get("PresetName", f"Preset {i+1}"))

		if self.presets:
			self.preset_list.setCurrentRow(0)

	def preset_changed(self, index):
		if 0 <= index < len(self.presets):
			self.current_preset_index = index
			self.load_preset_data()

	def load_preset_data(self):
		if self.current_preset_index < 0 or self.current_preset_index >= len(self.presets):
			return

		preset = self.presets[self.current_preset_index]

		# Temporarily disconnect signals to prevent unwanted updates during loading
		self.preset_name_edit.textChanged.disconnect(self.update_current_preset)
		self.leader_mesh_edit.textChanged.disconnect(self.update_current_preset)
		self.leader_material_edit.textChanged.disconnect(self.update_current_preset)
		self.mesh_table.itemChanged.disconnect(self.table_item_changed)

		try:
			# Load basic info
			self.preset_name_edit.setText(preset.get("PresetName", ""))
			self.leader_mesh_edit.setText(
				self.simplify_path(preset.get("LeaderMesh", ""), False)
			)
			self.leader_material_edit.setText(
				self.simplify_path(preset.get("LeaderMeshMaterial", ""), True)
			)

			# Load meshes and materials
			meshes = self.parse_array_string(preset.get("SkeletalMeshes", ""))
			materials = self.parse_array_string(preset.get("Materials", ""))

			self.mesh_table.setRowCount(len(meshes))

			for i, (mesh, material) in enumerate(zip(meshes, materials)):
				mesh_item = QTableWidgetItem(self.simplify_path(mesh, False))
				material_item = QTableWidgetItem(self.simplify_path(material, True))
				self.mesh_table.setItem(i, 0, mesh_item)
				self.mesh_table.setItem(i, 1, material_item)

		finally:
			# Reconnect signals
			self.preset_name_edit.textChanged.connect(self.update_current_preset)
			self.leader_mesh_edit.textChanged.connect(self.update_current_preset)
			self.leader_material_edit.textChanged.connect(self.update_current_preset)
			self.mesh_table.itemChanged.connect(self.table_item_changed)

	def update_current_preset(self):
		if self.current_preset_index < 0 or self.current_preset_index >= len(self.presets):
			return

		preset = self.presets[self.current_preset_index]

		# Update basic info
		preset["PresetName"] = self.preset_name_edit.text()
		preset["LeaderMesh"] = self.expand_path(self.leader_mesh_edit.text(), False)
		preset["LeaderMeshMaterial"] = self.expand_path(
			self.leader_material_edit.text(), True
		)

		# Update list widget item text
		if 0 <= self.current_preset_index < self.preset_list.count():
			self.preset_list.item(self.current_preset_index).setText(preset["PresetName"])

	def table_item_changed(self, item):
		if self.current_preset_index < 0 or self.current_preset_index >= len(self.presets):
			return

		# Collect all meshes and materials from table
		meshes = []
		materials = []

		for row in range(self.mesh_table.rowCount()):
			mesh_item = self.mesh_table.item(row, 0)
			material_item = self.mesh_table.item(row, 1)

			if mesh_item and material_item:
				mesh_text = mesh_item.text()
				material_text = material_item.text()

				meshes.append(self.expand_path(mesh_text, False))
				materials.append(self.expand_path(material_text, True))

		preset = self.presets[self.current_preset_index]
		preset["SkeletalMeshes"] = self.format_array_string(meshes)
		preset["Materials"] = self.format_array_string(materials)

	def add_mesh_material(self):
		row = self.mesh_table.rowCount()
		self.mesh_table.insertRow(row)

		# Add default items
		mesh_item = QTableWidgetItem("Meshes/Body/SK_NewMesh")
		material_item = QTableWidgetItem("Materials/Body/MI_NewMaterial")
		self.mesh_table.setItem(row, 0, mesh_item)
		self.mesh_table.setItem(row, 1, material_item)

		self.table_item_changed(None)  # Update the preset data

	def remove_mesh_material(self):
		current_row = self.mesh_table.currentRow()
		if current_row >= 0:
			self.mesh_table.removeRow(current_row)
			self.table_item_changed(None)  # Update the preset data

	def add_preset(self):
		new_preset = {
			"PresetName": f"PRESET_{len(self.presets) + 1}",
			"LeaderMesh": f"/Game/{self.character_name}/Meshes/Body/SK_{self.character_name}_Head.SK_{self.character_name}_Head",
			"LeaderMeshMaterial": f"/Game/{self.character_name}/Materials/Body/MI_{self.character_name}_Fullbody.MI_{self.character_name}_Fullbody",
			"SkeletalMeshes": "",
			"Materials": "",
		}

		self.presets.append(new_preset)
		self.update_preset_list()
		self.preset_list.setCurrentRow(len(self.presets) - 1)

	def duplicate_preset(self):
		if self.current_preset_index < 0 or self.current_preset_index >= len(self.presets):
			return

		current_preset = self.presets[self.current_preset_index].copy()
		current_preset["PresetName"] = f"{current_preset['PresetName']}_Copy"

		self.presets.append(current_preset)
		self.update_preset_list()
		self.preset_list.setCurrentRow(len(self.presets) - 1)

	def delete_preset(self):
		if self.current_preset_index < 0 or self.current_preset_index >= len(self.presets):
			return

		if len(self.presets) <= 1:
			QMessageBox.warning(self, "Warning", "Cannot delete the last preset!")
			return

		reply = QMessageBox.question(
			self,
			"Confirm Delete",
			f"Are you sure you want to delete preset '{self.presets[self.current_preset_index]['PresetName']}'?",
		)

		if reply == QMessageBox.StandardButton.Yes:
			del self.presets[self.current_preset_index]
			self.update_preset_list()

			# Select a valid preset
			if self.current_preset_index >= len(self.presets):
				self.preset_list.setCurrentRow(len(self.presets) - 1)
			else:
				self.preset_list.setCurrentRow(self.current_preset_index)


def main():
	app = QApplication(sys.argv)
	editor = PresetEditor()
	editor.show()
	sys.exit(app.exec())


if __name__ == "__main__":
	main()
