import sys
import csv
import ast
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QListWidget, QLineEdit, QLabel, QMessageBox, QListWidgetItem, QFileDialog
)

class PresetEditor(QWidget):
    def __init__(self, csv_path):
        super().__init__()
        self.setWindowTitle("Preset Editor")
        self.csv_path = csv_path
        self.presets = []
        self.character_name = None

        self.load_csv()
        self.setup_ui()
        self.populate_presets()

    def load_csv(self):
        with open(self.csv_path, newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            self.presets = []
            for row in reader:
                preset = dict(row)
                preset['SkeletalMeshes'] = ast.literal_eval(row['SkeletalMeshes'])
                preset['Materials'] = ast.literal_eval(row.get('Materiales', row.get('Materials')))
                self.presets.append(preset)
        if self.presets:
            self.character_name = self.extract_character_name(self.presets[0]['LeaderMesh'])

    def save_csv(self):
        with open(self.csv_path, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['PresetName', 'LeaderMesh', 'LeaderMeshMaterial', 'SkeletalMeshes', 'Materials']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for preset in self.presets:
                writer.writerow({
                    'PresetName': preset['PresetName'],
                    'LeaderMesh': preset['LeaderMesh'],
                    'LeaderMeshMaterial': preset['LeaderMeshMaterial'],
                    'SkeletalMeshes': str(preset['SkeletalMeshes']),
                    'Materials': str(preset['Materials'])
                })

    def extract_character_name(self, path):
        parts = Path(path).parts
        if 'Game' in parts:
            idx = parts.index('Game') + 1
            return parts[idx]
        return ""

    def simplify_path(self, path):
        return path.replace(f"/Game/{self.character_name}/", "")

    def full_path(self, short_path):
        parts = short_path.split('/')
        asset = parts[-1]
        return f"/Game/{self.character_name}/{'/'.join(parts)}.{asset}"

    def setup_ui(self):
        layout = QHBoxLayout(self)

        # Preset list
        self.preset_list = QListWidget()
        self.preset_list.currentRowChanged.connect(self.display_preset)
        layout.addWidget(self.preset_list)

        # Preset details
        detail_layout = QVBoxLayout()
        self.name_edit = QLineEdit()
        self.mesh_list = QListWidget()
        detail_layout.addWidget(QLabel("Preset Name:"))
        detail_layout.addWidget(self.name_edit)
        detail_layout.addWidget(QLabel("Skeletal Meshes & Materials:"))
        detail_layout.addWidget(self.mesh_list)

        btns = QHBoxLayout()
        add_btn = QPushButton("Add Mesh")
        remove_btn = QPushButton("Remove Mesh")
        duplicate_btn = QPushButton("Duplicate Preset")
        delete_btn = QPushButton("Delete Preset")
        save_btn = QPushButton("Save")

        add_btn.clicked.connect(self.add_mesh)
        remove_btn.clicked.connect(self.remove_mesh)
        duplicate_btn.clicked.connect(self.duplicate_preset)
        delete_btn.clicked.connect(self.delete_preset)
        save_btn.clicked.connect(self.save_changes)

        btns.addWidget(add_btn)
        btns.addWidget(remove_btn)
        btns.addWidget(duplicate_btn)
        btns.addWidget(delete_btn)
        btns.addWidget(save_btn)

        detail_layout.addLayout(btns)
        layout.addLayout(detail_layout)

    def populate_presets(self):
        self.preset_list.clear()
        for preset in self.presets:
            self.preset_list.addItem(preset['PresetName'])

    def display_preset(self, index):
        if index < 0 or index >= len(self.presets): return
        preset = self.presets[index]
        self.name_edit.setText(preset['PresetName'])
        self.mesh_list.clear()
        for sm, mat in zip(preset['SkeletalMeshes'], preset['Materials']):
            self.mesh_list.addItem(f"{self.simplify_path(sm)} | {self.simplify_path(mat)}")

    def add_mesh(self):
        sm, mat = "Meshes/New/SK_Example", "Materials/New/MI_Example"
        self.mesh_list.addItem(f"{sm} | {mat}")

    def remove_mesh(self):
        row = self.mesh_list.currentRow()
        if row != -1:
            self.mesh_list.takeItem(row)

    def duplicate_preset(self):
        idx = self.preset_list.currentRow()
        if idx == -1: return
        original = self.presets[idx].copy()
        original['PresetName'] += "_COPY"
        self.presets.append(original)
        self.populate_presets()

    def delete_preset(self):
        idx = self.preset_list.currentRow()
        if idx == -1: return
        del self.presets[idx]
        self.populate_presets()

    def save_changes(self):
        idx = self.preset_list.currentRow()
        if idx == -1: return
        preset = self.presets[idx]
        preset['PresetName'] = self.name_edit.text()
        skeletal_meshes = []
        materials = []
        for i in range(self.mesh_list.count()):
            item = self.mesh_list.item(i).text()
            sm_part, mat_part = item.split('|')
            skeletal_meshes.append(self.full_path(sm_part.strip()))
            materials.append(self.full_path(mat_part.strip()))
        preset['SkeletalMeshes'] = skeletal_meshes
        preset['Materials'] = materials
        self.presets[idx] = preset
        self.save_csv()
        QMessageBox.information(self, "Saved", "CSV file saved successfully.")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    editor = PresetEditor("Lia_Presets.csv")
    editor.resize(900, 500)
    editor.show()
    sys.exit(app.exec())
