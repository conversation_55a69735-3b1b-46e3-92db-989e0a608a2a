# lia_presets_editor.py
import csv
import json
import os
import sys
from typing import List, <PERSON><PERSON>

from PySide6.QtCore import QItemSelectionModel, Qt
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QListWidget, QPushButton, QTableWidget, QTableWidgetItem,
    QLineEdit, QMessageBox
)

CSV_FILE = "Lia_Presets.csv"


# -------------------------- Helper functions -----------------------------
def full_to_short(path: str, char: str) -> str:
    """/Game/Lia/Meshes/Body/SK_Lia_Head.SK_Lia_Head -> Meshes/Body/SK_Lia_Head"""
    prefix = f"/Game/{char}/"
    if path.startswith(prefix):
        path = path[len(prefix):]
    # remove .SK_xxx or .MI_xxx duplicate suffix
    if path.count('.') == 1:
        path = path.split('.')[0]
    return path


def short_to_full(short: str, char: str, is_material: bool) -> str:
    """Meshes/Body/SK_Lia_Head -> /Game/Lia/Meshes/Body/SK_Lia_Head.SK_Lia_Head"""
    suffix = short.split('/')[-1]
    if is_material:
        if not suffix.startswith("MI_"):
            suffix = "MI_" + suffix
        full_suffix = suffix
    else:
        if not suffix.startswith("SK_"):
            suffix = "SK_" + suffix
        full_suffix = suffix
    return f"/Game/{char}/{short}.{full_suffix}"


def load_csv(path: str) -> List[dict]:
    """Return list of dicts like {'PresetName': ..., 'SkeletalMeshes': [...], ...}"""
    rows = []
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Parse JSON-like arrays
            for key in ['SkeletalMeshes', 'Materiales']:
                if row[key].startswith('(') and row[key].endswith(')'):
                    row[key] = json.loads(row[key].replace("'", '"'))
                else:
                    row[key] = []
            rows.append(row)
    return rows


def save_csv(path: str, data: List[dict], char: str):
    """Write back the CSV in original format"""
    with open(path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['PresetName', 'LeaderMesh', 'LeaderMeshMaterial',
                         'SkeletalMeshes', 'Materiales'])
        for row in data:
            sk_arr = ['"{}"'.format(short_to_full(p, char, False))
                      for p in row['SkeletalMeshes']]
            mat_arr = ['"{}"'.format(short_to_full(p, char, True))
                       for p in row['Materiales']]
            sk_str = f"({','.join(sk_arr)})"
            mat_str = f"({','.join(mat_arr)})"
            writer.writerow([
                row['PresetName'],
                row['LeaderMesh'],
                row['LeaderMeshMaterial'],
                sk_str,
                mat_str
            ])


# ------------------------------ GUI --------------------------------------
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Lia Preset Editor")
        self.resize(900, 600)

        # Detect character name from first entry
        char = None
        if os.path.exists(CSV_FILE):
            sample = load_csv(CSV_FILE)[0]
            leader = sample['LeaderMesh']
            if leader.startswith("/Game/") and "/Meshes/" in leader:
                char = leader.split('/')[2]
        self.char = char or "Lia"

        self.data = load_csv(CSV_FILE) if os.path.exists(CSV_FILE) else []

        # Widgets
        self.list_presets = QListWidget()
        self.table = QTableWidget(0, 2)
        self.table.setHorizontalHeaderLabels(["SkeletalMesh", "Material"])

        btn_add_preset = QPushButton("Add preset")
        btn_dup_preset = QPushButton("Duplicate preset")
        btn_del_preset = QPushButton("Delete preset")
        btn_add_row = QPushButton("Add row")
        btn_del_row = QPushButton("Remove row")
        btn_save = QPushButton("Save")

        # Layouts
        left = QVBoxLayout()
        left.addWidget(self.list_presets)
        left.addWidget(btn_add_preset)
        left.addWidget(btn_dup_preset)
        left.addWidget(btn_del_preset)

        right = QVBoxLayout()
        right.addWidget(self.table)
        row_btns = QHBoxLayout()
        row_btns.addWidget(btn_add_row)
        row_btns.addWidget(btn_del_row)
        row_btns.addStretch()
        right.addLayout(row_btns)
        right.addWidget(btn_save)

        main = QHBoxLayout()
        main.addLayout(left, 1)
        main.addLayout(right, 3)

        w = QWidget()
        w.setLayout(main)
        self.setCentralWidget(w)

        # Connections
        self.list_presets.currentRowChanged.connect(self.load_preset)
        btn_add_preset.clicked.connect(self.add_preset)
        btn_dup_preset.clicked.connect(self.duplicate_preset)
        btn_del_preset.clicked.connect(self.delete_preset)
        btn_add_row.clicked.connect(self.add_row)
        btn_del_row.clicked.connect(self.remove_row)
        btn_save.clicked.connect(self.save)

        self.refresh_list()

    # ---------------------- List handling -------------------------------
    def refresh_list(self):
        self.list_presets.clear()
        for row in self.data:
            self.list_presets.addItem(row['PresetName'])
        if self.data:
            self.list_presets.setCurrentRow(0)

    def current_preset(self) -> dict:
        idx = self.list_presets.currentRow()
        return self.data[idx] if 0 <= idx < len(self.data) else None

    def load_preset(self):
        preset = self.current_preset()
        if not preset:
            return
        sk = preset['SkeletalMeshes']
        mat = preset['Materiales']

        self.table.setRowCount(max(len(sk), len(mat)))
        for i, (s, m) in enumerate(zip(sk, mat)):
            short_s = full_to_short(s, self.char)
            short_m = full_to_short(m, self.char)
            self.table.setItem(i, 0, QTableWidgetItem(short_s))
            self.table.setItem(i, 1, QTableWidgetItem(short_m))
        # Fill remaining rows if lengths differ
        for i in range(min(len(sk), len(mat)), max(len(sk), len(mat))):
            self.table.setItem(i, 0, QTableWidgetItem(""))
            self.table.setItem(i, 1, QTableWidgetItem(""))

    def add_preset(self):
        name, ok = QLineEdit.getText(self, "New preset", "Preset name:")
        if ok and name:
            self.data.append({
                'PresetName': name,
                'LeaderMesh': f"/Game/{self.char}/Meshes/Body/SK_{self.char}_Head.SK_{self.char}_Head",
                'LeaderMeshMaterial': f"/Game/{self.char}/Materials/Body/MI_{self.char}_Fullbody.MI_{self.char}_Fullbody",
                'SkeletalMeshes': [],
                'Materiales': []
            })
            self.refresh_list()
            self.list_presets.setCurrentRow(len(self.data) - 1)

    def duplicate_preset(self):
        preset = self.current_preset()
        if not preset:
            return
        dup = {k: v[:] if isinstance(v, list) else v for k, v in preset.items()}
        dup['PresetName'] += "_Copy"
        self.data.append(dup)
        self.refresh_list()
        self.list_presets.setCurrentRow(len(self.data) - 1)

    def delete_preset(self):
        idx = self.list_presets.currentRow()
        if idx < 0:
            return
        del self.data[idx]
        self.refresh_list()

    # ---------------------- Table handling ------------------------------
    def add_row(self):
        row = self.table.rowCount()
        self.table.insertRow(row)
        self.table.setItem(row, 0, QTableWidgetItem(""))
        self.table.setItem(row, 1, QTableWidgetItem(""))

    def remove_row(self):
        rows = set(idx.row() for idx in self.table.selectedIndexes())
        for r in sorted(rows, reverse=True):
            self.table.removeRow(r)

    # ---------------------- Save ----------------------------------------
    def save(self):
        preset = self.current_preset()
        if not preset:
            return

        sk, mat = [], []
        for r in range(self.table.rowCount()):
            s_item = self.table.item(r, 0)
            m_item = self.table.item(r, 1)
            s = (s_item.text() if s_item else "").strip()
            m = (m_item.text() if m_item else "").strip()
            if s:
                sk.append(s)
            if m:
                mat.append(m)
        preset['SkeletalMeshes'] = sk
        preset['Materiales'] = mat

        save_csv(CSV_FILE, self.data, self.char)
        QMessageBox.information(self, "Saved", "CSV saved successfully.")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())